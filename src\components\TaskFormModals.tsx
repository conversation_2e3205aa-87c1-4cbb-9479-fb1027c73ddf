import React, { useEffect } from "react";
import {
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Row,
  Col,
  Table,
  Button,
  Space,
  InputNumber,
  Tag,
} from "antd";
import type {
  task_alert,
  db_connection,
  alert_send,
  other_info,
} from "../types/task";
import {
  DB_TYPE_OPTIONS,
  ALERT_TYPE_OPTIONS,
  ALERT_SEVERITY_OPTIONS,
  SEND_TYPE_OPTIONS,
  ORACLE_CONNECT_METHOD_OPTIONS,
} from "../types/task";

const { Option } = Select;
const { TextArea } = Input;

interface AlertModalProps {
  visible: boolean;
  editingData?: task_alert;
  onCancel: () => void;
  onSubmit: (data: task_alert) => void;
}

interface DbConnectionModalProps {
  visible: boolean;
  editingData?: db_connection;
  onCancel: () => void;
  onSubmit: (data: db_connection) => void;
}

interface AlertSendModalProps {
  visible: boolean;
  editingData?: alert_send;
  onCancel: () => void;
  onSubmit: (data: alert_send) => void;
}

interface OtherInfoModalProps {
  visible: boolean;
  editingData?: other_info;
  onCancel: () => void;
  onSubmit: (data: other_info) => void;
}

interface SelectModalProps {
  visible: boolean;
  type: "alert" | "alertSend" | "dbConnection" | "otherInfo";
  data: any[];
  onCancel: () => void;
  onSubmit: (selectedItems: any[]) => void;
  multiple?: boolean;
}

// 告警Modal
export const AlertModal: React.FC<AlertModalProps> = ({
  visible,
  editingData,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue(editingData);
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async (values: any) => {
    const alertData: task_alert = {
      id: editingData?.id || Date.now(),
      ...values,
      values: values.type === "isEqual" ? values.values || [] : [],
    };
    onSubmit(alertData);
  };

  return (
    <Modal
      title={editingData ? "编辑告警规则" : "新增告警规则"}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="告警名称"
              name="name"
              rules={[{ required: true, message: "请输入告警名称" }]}
            >
              <Input placeholder="请输入告警名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="告警级别"
              name="severity"
              rules={[{ required: true, message: "请选择告警级别" }]}
            >
              <Select placeholder="请选择告警级别">
                {ALERT_SEVERITY_OPTIONS.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          label="告警类型"
          name="type"
          rules={[{ required: true, message: "请选择告警类型" }]}
        >
          <Select placeholder="请选择告警类型">
            {ALERT_TYPE_OPTIONS.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          label="SQL语句"
          name="sql"
          rules={[{ required: true, message: "请输入SQL语句" }]}
        >
          <TextArea rows={4} placeholder="请输入SQL语句" />
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.type !== currentValues.type
          }
        >
          {({ getFieldValue }) =>
            getFieldValue("type") === "isEqual" ? (
              <Form.Item
                label="触发值"
                name="values"
                rules={[{ required: true, message: "请输入触发值" }]}
              >
                <Select
                  mode="tags"
                  placeholder="请输入触发值，支持多个值"
                  style={{ width: "100%" }}
                />
              </Form.Item>
            ) : null
          }
        </Form.Item>

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" htmlType="submit">
            保存
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

// 数据库连接Modal
export const DbConnectionModal: React.FC<DbConnectionModalProps> = ({
  visible,
  editingData,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue(editingData);
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async (values: any) => {
    const dbData: db_connection = {
      id: editingData?.id || Date.now(),
      ...values,
    };
    onSubmit(dbData);
  };

  return (
    <Modal
      title={editingData ? "编辑数据库连接" : "新增数据库连接"}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={800}
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="连接名称"
              name="name"
              rules={[{ required: true, message: "请输入连接名称" }]}
            >
              <Input placeholder="请输入连接名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="数据库类型"
              name="db_type"
              rules={[{ required: true, message: "请选择数据库类型" }]}
            >
              <Select placeholder="请选择数据库类型">
                {DB_TYPE_OPTIONS.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={16}>
            <Form.Item
              label="主机地址"
              name="host"
              rules={[{ required: true, message: "请输入主机地址" }]}
            >
              <Input placeholder="请输入主机地址" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="端口"
              name="port"
              rules={[{ required: true, message: "请输入端口" }]}
            >
              <Input placeholder="请输入端口" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="用户名"
              name="user"
              rules={[{ required: true, message: "请输入用户名" }]}
            >
              <Input placeholder="请输入用户名" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="密码"
              name="passwd"
              rules={[{ required: true, message: "请输入密码" }]}
            >
              <Input.Password placeholder="请输入密码" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.db_type !== currentValues.db_type
          }
        >
          {({ getFieldValue }) =>
            getFieldValue("db_type") === "mysql" ? (
              <>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="数据库名"
                      name="database"
                      rules={[{ required: true, message: "请输入数据库名" }]}
                    >
                      <Input placeholder="请输入数据库名" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="服务器时区"
                      name="serverTimezone"
                      initialValue="Asia/Shanghai"
                    >
                      <Input placeholder="请输入服务器时区" />
                    </Form.Item>
                  </Col>
                </Row>
                <Form.Item
                  label="使用SSL"
                  name="useSSL"
                  valuePropName="checked"
                  initialValue={false}
                >
                  <Switch />
                </Form.Item>
              </>
            ) : getFieldValue("db_type") === "oracle" ? (
              <>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="实例名"
                      name="instance"
                      rules={[{ required: true, message: "请输入实例名" }]}
                    >
                      <Input placeholder="请输入实例名" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="连接方式"
                      name="connectMethod"
                      rules={[{ required: true, message: "请选择连接方式" }]}
                    >
                      <Select placeholder="请选择连接方式">
                        {ORACLE_CONNECT_METHOD_OPTIONS.map((option) => (
                          <Option key={option.value} value={option.value}>
                            {option.label}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </>
            ) : null
          }
        </Form.Item>

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" htmlType="submit">
            保存
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

// 告警发送Modal
export const AlertSendModal: React.FC<AlertSendModalProps> = ({
  visible,
  editingData,
  onCancel,
  onSubmit,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue(editingData);
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async (values: any) => {
    const alertSendData: alert_send = {
      id: editingData?.id || Date.now(),
      ...values,
    };
    onSubmit(alertSendData);
  };

  return (
    <Modal
      title={editingData ? "编辑告警发送" : "新增告警发送"}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
    >
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="发送名称"
              name="name"
              rules={[{ required: true, message: "请输入发送名称" }]}
            >
              <Input placeholder="请输入发送名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="发送类型"
              name="type"
              rules={[{ required: true, message: "请选择发送类型" }]}
            >
              <Select placeholder="请选择发送类型">
                {SEND_TYPE_OPTIONS.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          label="主机地址"
          name="host"
          rules={[{ required: true, message: "请输入主机地址" }]}
        >
          <Input placeholder="请输入主机地址" />
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.type !== currentValues.type
          }
        >
          {({ getFieldValue }) =>
            getFieldValue("type") === "kafka" ? (
              <Form.Item
                label="Topic"
                name="topic"
                rules={[{ required: true, message: "请输入Topic" }]}
              >
                <Input placeholder="请输入Topic" />
              </Form.Item>
            ) : null
          }
        </Form.Item>

        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" htmlType="submit">
            保存
          </Button>
        </div>
      </Form>
    </Modal>
  );
};
