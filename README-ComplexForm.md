# 复合任务表单功能说明

## 功能概述

本次更新将原有的简单任务表格替换为基于 `task_exec` 的复合表单系统，支持多表关联的复杂数据管理。

## 数据结构变更

### 主表：task_exec
- `id`: 主键
- `name`: 任务名称（唯一）
- `group`: 任务分组
- `enable`: 是否启用
- `start_time`: 开始时间 (HH:mm:ss)
- `end_time`: 结束时间 (HH:mm:ss)
- `weekday`: 星期 (1-7 或 1,2,3,4,5)
- `frequency`: 执行频率
- `retryNum`: 重试次数
- `retry_frequency`: 重试间隔
- `alert_task_id`: 告警任务ID数组
- `alert_send_id`: 告警发送ID数组
- `db_connection_id`: 数据库连接ID
- `other_info_id`: 其他信息ID

### 关联表

#### task_alert（一对多）
- `id`: 主键
- `name`: 告警名称（唯一）
- `severity`: 告警级别 (low/medium/high/critical)
- `sql`: 告警对象SQL语句
- `type`: 告警类型 (isExist/isEqual)
- `values`: 触发值数组

#### db_connection（一对一）
- `id`: 主键
- `name`: 连接名称（唯一）
- `db_type`: 数据库类型 (mysql/oracle)
- `host`: 主机地址
- `port`: 端口
- `user`: 用户名
- `passwd`: 密码（base64加密）
- `database`: MySQL数据库名
- `useSSL`: 是否使用SSL
- `serverTimezone`: 服务器时区
- `instance`: Oracle实例名
- `connectMethod`: Oracle连接方式 (sid/service)

#### alert_send（一对多）
- `id`: 主键
- `name`: 发送名称（唯一）
- `type`: 接收类型 (kafka/prometheus)
- `host`: 主机地址
- `topic`: Kafka Topic

#### other_info（一对一）
- `id`: 主键
- `name`: 信息名称（唯一）
- `business`: 业务系统名称
- `businessEn`: 业务系统英文名称
- `hostname`: 主机名称
- `location`: 告警来源

## 新增功能

### 1. 复合表单组件 (ComplexTaskForm)
- 支持多标签页展示不同类型的配置
- 基本信息：task_exec 配置
- 告警配置：task_alert 管理
- 数据库连接：db_connection 配置
- 告警发送：alert_send 管理
- 其他信息：other_info 配置

### 2. 模态框组件
- `AlertModal`: 告警规则新增/编辑
- `DbConnectionModal`: 数据库连接新增/编辑
- `AlertSendModal`: 告警发送新增/编辑
- `OtherInfoModal`: 其他信息新增/编辑
- `SelectModal`: 选择已有数据

### 3. 表格功能增强
- 表格数据结构更新为 task_exec
- 新增"启用状态"列
- 更新"数据库连接"列显示
- 抽屉宽度调整为1200px以适应复合表单

### 4. 数据管理功能
- **新增数据**：每个子表都支持在表单中直接新增
- **选择已有**：可以从现有数据中选择并关联
- **编辑功能**：支持对已关联的数据进行编辑
- **删除功能**：支持删除已关联的数据

## 使用方式

### 新增任务
1. 点击"新增任务"按钮
2. 在抽屉中填写基本信息
3. 切换到其他标签页配置相关数据：
   - 告警配置：可新增告警规则或选择已有规则
   - 数据库连接：配置数据库连接信息
   - 告警发送：配置告警发送方式
   - 其他信息：配置业务系统相关信息
4. 提交保存

### 编辑任务
1. 点击表格中的"编辑"按钮
2. 在抽屉中修改各项配置
3. 支持对关联数据的增删改操作
4. 提交保存更改

### 数据选择
- 在各个配置标签页中，点击"选择已有"按钮
- 在弹出的选择框中勾选需要的数据
- 支持多选（告警规则、告警发送）和单选（数据库连接、其他信息）

## 技术实现

### 组件结构
```
src/components/
├── ComplexTaskForm.tsx          # 主复合表单组件
├── TaskFormModals.tsx           # 模态框组件（告警、数据库连接、告警发送）
├── TaskFormModalsExtended.tsx   # 扩展模态框组件（其他信息、选择框）
└── AntdTable.tsx               # 更新后的表格组件
```

### 服务层更新
- 新增 `getAlerts()`, `getDbConnections()`, `getAlertSends()`, `getOtherInfos()` 方法
- 新增 `saveComplexForm()`, `updateComplexForm()` 方法
- 更新模拟数据以支持新的数据结构

### 类型定义
- 新增 `ComplexFormData` 接口
- 更新各种选项常量的导出方式
- 保持向后兼容的 `TaskData` 接口

## 注意事项

1. 数据库连接和其他信息为一对一关系，只能选择一个
2. 告警规则和告警发送为一对多关系，可以选择多个
3. 所有子表数据都支持新增和选择已有数据两种方式
4. 表单验证确保必填字段的完整性
5. 支持热重载，开发时修改代码会自动更新页面
