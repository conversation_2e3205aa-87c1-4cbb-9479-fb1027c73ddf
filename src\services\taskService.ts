import type {
  TaskData,
  TaskFormData,
  TaskSearchParams,
  TaskApiResponse,
  task_exec,
  task_alert,
  db_connection,
  alert_send,
  other_info,
  ComplexFormData
} from '../types/task';

/**
 * 生成模拟任务数据
 */
function generateMockTasks(count: number): TaskData[] {
  const tasks: TaskData[] = [];
  const groups = ['系统维护', '数据备份', '监控告警', '日志清理', '性能优化'];
  const frequencies = ['daily', 'weekly', 'monthly', 'custom'];
  const statuses: ('enabled' | 'disabled')[] = ['enabled', 'disabled'];

  for (let i = 1; i <= count; i++) {
    const startHour = Math.floor(Math.random() * 24);
    const startMinute = Math.floor(Math.random() * 60);
    const endHour = (startHour + Math.floor(Math.random() * 3) + 1) % 24;
    const endMinute = Math.floor(Math.random() * 60);

    tasks.push({
      id: i,
      name: `任务${i.toString().padStart(3, '0')}`,
      group: groups[Math.floor(Math.random() * groups.length)],
      status: Math.random() > 0.3 ? 'enabled' : 'disabled', // 70%的任务启用
      start_time: `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}:00`,
      end_time: `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}:00`,
      weekday: Math.random() > 0.5 ?
        Math.floor(Math.random() * 7 + 1).toString() :
        '1,2,3,4,5',
      frequency: frequencies[Math.floor(Math.random() * frequencies.length)],
      retryNum: Math.floor(Math.random() * 5).toString(),
      retry_frequency: `${Math.floor(Math.random() * 60 + 1)}分钟`,
      alert_task_id: [`alert_${Math.floor(Math.random() * 10) + 1}`],
      alert_send_id: [`send_${Math.floor(Math.random() * 5) + 1}`],
      db_connection_id: `db_${Math.floor(Math.random() * 3) + 1}`,
      other_info_id: `info_${Math.floor(Math.random() * 5) + 1}`,
      createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' '),
    });
  }

  return tasks;
}

/**
 * 模拟数据 - 生成1000条任务数据
 */
export const mockTaskData: TaskData[] = generateMockTasks(1000);

/**
 * 模拟告警数据
 */
export const mockAlertData: task_alert[] = [
  { id: 1, name: '数据库连接检查', severity: 'high', sql: 'SELECT 1 FROM dual', type: 'isExist', values: [] },
  { id: 2, name: '表记录数检查', severity: 'medium', sql: 'SELECT COUNT(*) FROM users', type: 'isEqual', values: ['100'] },
  { id: 3, name: '系统状态检查', severity: 'critical', sql: 'SELECT status FROM system_status', type: 'isEqual', values: ['active'] },
];

/**
 * 模拟数据库连接数据
 */
export const mockDbConnectionData: db_connection[] = [
  {
    id: 1, name: '主数据库', db_type: 'mysql', host: '*************', port: '3306',
    user: 'admin', passwd: 'cGFzc3dvcmQ=', database: 'main_db', useSSL: false,
    serverTimezone: 'Asia/Shanghai', instance: '', connectMethod: ''
  },
  {
    id: 2, name: 'Oracle数据库', db_type: 'oracle', host: '*************', port: '1521',
    user: 'oracle', passwd: 'b3JhY2xl', database: '', useSSL: false,
    serverTimezone: '', instance: 'ORCL', connectMethod: 'sid'
  },
];

/**
 * 模拟告警发送数据
 */
export const mockAlertSendData: alert_send[] = [
  { id: 1, name: 'Kafka告警', type: 'kafka', host: '*************:9092', topic: 'alerts' },
  { id: 2, name: 'Prometheus告警', type: 'prometheus', host: '*************:9090', topic: '' },
];

/**
 * 模拟其他信息数据
 */
export const mockOtherInfoData: other_info[] = [
  { id: 1, name: '用户系统', business: '用户管理系统', businessEn: 'user-management', hostname: 'user-server-01', location: '北京机房' },
  { id: 2, name: '订单系统', business: '订单管理系统', businessEn: 'order-management', hostname: 'order-server-01', location: '上海机房' },
];

/**
 * 任务API服务类
 */
export class TaskService {
  /**
   * 获取任务列表
   * @param params 搜索参数
   * @returns 任务列表和总数
   */
  static async getTasks(params: TaskSearchParams): Promise<TaskApiResponse<TaskData>> {
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 300));

    // 模拟分页和搜索逻辑
    let filteredData = [...mockTaskData];
    
    // 根据搜索条件过滤数据
    if (params.name) {
      filteredData = filteredData.filter(item => 
        item.name.includes(params.name!)
      );
    }
    
    if (params.group) {
      filteredData = filteredData.filter(item => 
        item.group.includes(params.group!)
      );
    }

    if (params.status) {
      filteredData = filteredData.filter(item => 
        item.status === params.status
      );
    }

    if (params.weekday) {
      filteredData = filteredData.filter(item => 
        item.weekday.includes(params.weekday!)
      );
    }

    if (params.frequency) {
      filteredData = filteredData.filter(item =>
        item.frequency === params.frequency
      );
    }



    // 分页处理
    const { current = 1, pageSize = 10 } = params;
    const startIndex = (current - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = filteredData.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      total: filteredData.length,
      success: true,
    };
  }

  /**
   * 删除单个任务
   * @param id 任务ID
   * @returns 是否删除成功
   */
  static async deleteTask(id: number): Promise<boolean> {
    console.log("删除任务:", id);
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500));
    
    return true; // 模拟成功
  }

  /**
   * 批量删除任务
   * @param ids 任务ID数组
   * @returns 是否删除成功
   */
  static async batchDeleteTasks(ids: number[]): Promise<boolean> {
    console.log("批量删除任务:", ids);
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 800));
    
    return true; // 模拟成功
  }

  /**
   * 添加任务
   * @param taskData 任务数据
   * @returns 新创建的任务信息
   */
  static async addTask(taskData: TaskFormData): Promise<TaskData> {
    console.log("添加任务:", taskData);
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 600));
    
    // 模拟返回新创建的任务
    const newTask: TaskData = {
      id: Date.now(),
      ...taskData,
      createTime: new Date().toLocaleString(),
    };
    
    return newTask;
  }

  /**
   * 更新任务
   * @param id 任务ID
   * @param taskData 更新的任务数据
   * @returns 更新后的任务信息
   */
  static async updateTask(id: number, taskData: Partial<TaskFormData>): Promise<TaskData> {
    console.log("更新任务:", id, taskData);
    // 模拟API调用延迟
    await new Promise((resolve) => setTimeout(resolve, 500));
    
    // 模拟返回更新后的任务
    const existingTask = mockTaskData.find(task => task.id === id);
    if (!existingTask) {
      throw new Error('任务不存在');
    }
    
    return {
      ...existingTask,
      ...taskData,
    };
  }

  /**
   * 获取告警列表
   */
  static async getAlerts(): Promise<task_alert[]> {
    await new Promise((resolve) => setTimeout(resolve, 200));
    return mockAlertData;
  }

  /**
   * 获取数据库连接列表
   */
  static async getDbConnections(): Promise<db_connection[]> {
    await new Promise((resolve) => setTimeout(resolve, 200));
    return mockDbConnectionData;
  }

  /**
   * 获取告警发送列表
   */
  static async getAlertSends(): Promise<alert_send[]> {
    await new Promise((resolve) => setTimeout(resolve, 200));
    return mockAlertSendData;
  }

  /**
   * 获取其他信息列表
   */
  static async getOtherInfos(): Promise<other_info[]> {
    await new Promise((resolve) => setTimeout(resolve, 200));
    return mockOtherInfoData;
  }

  /**
   * 保存复合表单数据
   */
  static async saveComplexForm(data: ComplexFormData): Promise<TaskData> {
    console.log("保存复合表单数据:", data);
    await new Promise((resolve) => setTimeout(resolve, 800));

    // 模拟返回新创建的任务
    const newTask: TaskData = {
      id: Date.now(),
      ...data.task_exec,
      createTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
      status: 'enabled',
    };

    return newTask;
  }

  /**
   * 更新复合表单数据
   */
  static async updateComplexForm(id: number, data: ComplexFormData): Promise<TaskData> {
    console.log("更新复合表单数据:", id, data);
    await new Promise((resolve) => setTimeout(resolve, 800));

    const existingTask = mockTaskData.find(task => task.id === id);
    if (!existingTask) {
      throw new Error('任务不存在');
    }

    return {
      ...existingTask,
      ...data.task_exec,
    };
  }
}
